name: Unit tests

on: [push, pull_request]

jobs:
  unit-tests:
    name: Unit tests
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.8", "3.9", "3.10", "3.11", "3.12"]
    steps:
    - name: Checkout repo
      uses: actions/checkout@v4
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -e .
        pip install tox tox-gh-actions coveralls
        bash ryu/tests/integrated/common/install_docker_test_pkg_for_github_actions.sh
    - name: Test with tox
      run: PYTEST_VERBOSE=1 tox
