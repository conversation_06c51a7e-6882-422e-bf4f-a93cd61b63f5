Source: ryu
Section: net
Priority: optional
Maintainer: Ryu Project Team <<EMAIL>>
Build-Depends: debhelper (>= 9.0.0), python-all (>= 2.6), python-sphinx
Build-Depends-Indep:
 python-eventlet,
 python-lxml,
 python-msgpack (>= 0.4.0),
 python-netaddr,
 python-oslo.config (>= 1:1.2.0),
 python-paramiko,
 python-routes,
 python-six (>= 1.4.0),
 python-webob (>=1.2),
 python-setuptools,
 python-pip,
 python-pbr
Standards-Version: 3.9.5
Homepage: https://ryu-sdn.org
Vcs-Git: git://github.com/faucetsdn/ryu.git
Vcs-Browser: https://github.com/faucetsdn/ryu
XS-Python-Version: >= 2.6

Package: python-ryu
Architecture: all
Section: python
Depends:
 python-eventlet,
 python-lxml,
 python-msgpack (>= 0.4.0),
 python-netaddr,
 python-oslo.config (>= 1:1.2.0),
 python-paramiko,
 python-routes,
 python-six (>= 1.4.0),
 python-webob (>=1.2),
 ${misc:Depends},
 ${python:Depends}
Suggests: python-ryu-doc
Provides: ${python:Provides}
XB-Python-Version: ${python:Versions}
Description: Ryu is a software defined networking framework
 Ryu is a component-based software defined networking framework.
 Ryu provides software components with well defined API that make
 it easy for developers to create new network management and control
 applications. Ryu supports various protocols for managing network
 devices, such as OpenFlow, Netconf, OF-config, etc. About OpenFlow,
 Ryu supports fully 1.0, 1.2, 1.3, 1.4 and Nicira Extensions.
 .
 This package provides the Python library.

Package: ryu-bin
Architecture: all
Depends:
 python-ryu,
 ${misc:Depends},
 ${python:Depends},
 ${shlibs:Depends}
Description: Ryu is a software defined networking framework
 Ryu is a component-based software defined networking framework.
 Ryu provides software components with well defined API that make
 it easy for developers to create new network management and control
 applications. Ryu supports various protocols for managing network
 devices, such as OpenFlow, Netconf, OF-config, etc. About OpenFlow,
 Ryu supports fully 1.0, 1.2, 1.3, 1.4 and Nicira Extensions.
 .
 This package provides the Ryu manager.

Package: python-ryu-doc
Architecture: all
Section: doc
Description: Ryu is a software defined networking framework
 Ryu is a component-based software defined networking framework.
 Ryu provides software components with well defined API that make
 it easy for developers to create new network management and control
 applications. Ryu supports various protocols for managing network
 devices, such as OpenFlow, Netconf, OF-config, etc. About OpenFlow,
 Ryu supports fully 1.0, 1.2, 1.3, 1.4 and Nicira Extensions.
 .
 This package provides the HTML documentation including the Ryu API
 manual.
