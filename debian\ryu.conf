[DEFAULT]
log_config_file=/etc/ryu/log.conf
# app_lists = $RYU_APPS
#
# for OpenStack Neutron Ryu plugin:
# mac address based isolation
#app_lists = ryu.app.simple_isolation,ryu.app.rest
# VLAN
#app_lists=ryu.app.quantum_adapter,ryu.app.rest,ryu.app.rest_conf_switch,ryu.app.rest_quantum,ryu.app.rest_tunnel,ryu.app.simple_vlan
# GRE tunneling
#app_lists=ryu.app.gre_tunnel,ryu.app.quantum_adapter,ryu.app.rest,ryu.app.rest_conf_switch,ryu.app.rest_quantum,ryu.app.rest_tunnel,ryu.app.tunnel_port_updater
#
# wsapi_host=<hostip>
# wsapi_port=<port:8080>
# ofp_listen_host=<hostip>
# ofp_tcp_listen_port=<port:6633>
#wsapi_host = 0.0.0.0
#wsapi_port = 8080
#ofp_listen_host = 0.0.0.0
#ofp_tcp_listen_port = 6633
#
# the followings must be set according to neutron settings
# neutron_url = http://$Q_HOST:$Q_PORT
# neutron_admin_username = $Q_ADMIN_USERNAME
# neutron_admin_password = $SERVICE_PASSWORD
# neutron_admin_tenant_name = $SERVICE_TENANT_NAME
# neutron_admin_auth_url=$KEYSTONE_SERVICE_PROTOCOL://$KEYSTONE_SERVICE_HOST:$KEYSTONE_AUTH_PORT/v2.0
# neutron_auth_strategy = $Q_AUTH_STRATEGY
# neutron_controller_addr = tcp:$RYU_OFP_HOST:$RYU_OFP_PORT
#neutron_url = http://***********:9696
#neutron_admin_username = admin
#neutron_admin_password = password
#neutron_admin_tenant_name = service
#neutron_admin_auth_url = http://***********:5000/v2.0
#neutron_auth_strategy = keystone
#neutron_controller_addr = tcp:***********:6633
