.. _using_with_openstack:

************************************************************************
Using Ryu Network Operating System with OpenStack as OpenFlow controller
************************************************************************

.. CAUTION::

    The Ryu plugin and OFAgent described in the following is deprecated,
    because <PERSON><PERSON> is officially integrated into Open vSwitch agent with
    "of_interface = native" mode.


<PERSON><PERSON> cooperates with OpenStack using Quantum Ryu plugin. The plugin is
available in the official Quantum releases.

For more information, please visit https://github.com/faucetsdn/ryu/wiki/OpenStack .
We described instructions of the installation / configuration of OpenStack
with Ryu, and we provide pre-configured VM image to be able to easily try
OpenStack with <PERSON><PERSON>.

----

* OpenStack: http://www.openstack.org/
* Quantum: https://github.com/openstack/quantum/
