/* Content Area */
.content-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* Graph Container */
.graph-container {
    flex: 1;
    position: relative;
    background-color: var(--bg-primary);
    min-height: 300px;
}

.graph-wrapper {
    position: relative;
    width: 100%;
    height: 100%;
}

.graph-canvas {
    width: 100%;
    height: 100%;
    background-color: var(--bg-primary);
}

/* Graph Overlay */
.graph-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    z-index: 10;
}

/* Loading Spinner */
.loading-spinner {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

.loading-spinner.hidden {
    display: none;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 3px solid var(--border-color);
    border-top: 3px solid var(--primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Mini-map */
.minimap-container {
    position: absolute;
    bottom: 1rem;
    right: 1rem;
    width: 200px;
    height: 150px;
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: 0 4px 8px var(--shadow);
    z-index: 20;
    transition: var(--transition);
}

.minimap-container.hidden {
    display: none;
}

.minimap-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.5rem;
    background-color: var(--bg-tertiary);
    border-bottom: 1px solid var(--border-color);
    border-radius: var(--border-radius) var(--border-radius) 0 0;
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
}

.minimap-toggle {
    background: none;
    border: none;
    font-size: 1rem;
    cursor: pointer;
    color: var(--text-secondary);
    transition: var(--transition);
}

.minimap-toggle:hover {
    color: var(--text-primary);
}

.minimap {
    width: 100%;
    height: calc(100% - 32px);
    background-color: var(--bg-primary);
}

/* Detail Panel */
.detail-panel {
    position: fixed;
    top: var(--header-height);
    right: -400px;
    width: 400px;
    height: calc(100vh - var(--header-height));
    background-color: var(--bg-secondary);
    border-left: 1px solid var(--border-color);
    box-shadow: -4px 0 8px var(--shadow);
    z-index: 200;
    transition: var(--transition);
    overflow-y: auto;
}

.detail-panel.open {
    right: 0;
}

.detail-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
    background-color: var(--bg-tertiary);
    border-bottom: 1px solid var(--border-color);
    position: sticky;
    top: 0;
    z-index: 10;
}

.detail-header h3 {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-medium);
    color: var(--text-primary);
}

.detail-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--text-secondary);
    transition: var(--transition);
    padding: 0.25rem;
    border-radius: var(--border-radius);
}

.detail-close:hover {
    color: var(--text-primary);
    background-color: var(--bg-primary);
}

.detail-content {
    padding: 1rem;
}

/* Detail Content Components */
.detail-section {
    margin-bottom: 1.5rem;
}

.detail-section:last-child {
    margin-bottom: 0;
}

.detail-section-title {
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-medium);
    color: var(--text-primary);
    margin-bottom: 0.75rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid var(--border-color);
}

.detail-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 0.5rem;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 0.5rem;
    background-color: var(--bg-primary);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
}

.detail-label {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    font-weight: var(--font-weight-medium);
    min-width: 100px;
}

.detail-value {
    font-size: var(--font-size-sm);
    color: var(--text-primary);
    text-align: right;
    word-break: break-all;
}

.detail-value.code {
    font-family: 'Courier New', monospace;
    background-color: var(--bg-tertiary);
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.75rem;
}

/* Node Type Indicators */
.node-type-indicator {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
}

.node-type-indicator.switch-openflow {
    background-color: rgba(0, 123, 255, 0.1);
    color: var(--switch-openflow);
    border: 1px solid rgba(0, 123, 255, 0.3);
}

.node-type-indicator.switch-p4runtime {
    background-color: rgba(40, 167, 69, 0.1);
    color: var(--switch-p4runtime);
    border: 1px solid rgba(40, 167, 69, 0.3);
}

.node-type-indicator.host {
    background-color: rgba(255, 193, 7, 0.1);
    color: var(--host-color);
    border: 1px solid rgba(255, 193, 7, 0.3);
}

/* Port List */
.port-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.port-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.5rem;
    background-color: var(--bg-primary);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
}

.port-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.port-name {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--text-primary);
}

.port-details {
    font-size: 0.75rem;
    color: var(--text-secondary);
    font-family: 'Courier New', monospace;
}

.port-status {
    padding: 0.125rem 0.375rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: var(--font-weight-medium);
}

.port-status.up {
    background-color: rgba(40, 167, 69, 0.1);
    color: var(--success);
    border: 1px solid rgba(40, 167, 69, 0.3);
}

.port-status.down {
    background-color: rgba(220, 53, 69, 0.1);
    color: var(--danger);
    border: 1px solid rgba(220, 53, 69, 0.3);
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: calc(var(--header-height) + 1rem);
    right: 1rem;
    z-index: 1000;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    max-width: 400px;
}

.toast {
    padding: 1rem;
    border-radius: var(--border-radius);
    box-shadow: 0 4px 8px var(--shadow);
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    animation: slideIn 0.3s ease-out;
    position: relative;
    overflow: hidden;
}

.toast.success {
    background-color: var(--success);
    color: white;
}

.toast.error {
    background-color: var(--danger);
    color: white;
}

.toast.warning {
    background-color: var(--warning);
    color: var(--text-primary);
}

.toast.info {
    background-color: var(--info);
    color: white;
}

.toast-icon {
    font-size: 1.25rem;
    flex-shrink: 0;
}

.toast-content {
    flex: 1;
}

.toast-title {
    font-weight: var(--font-weight-medium);
    margin-bottom: 0.25rem;
}

.toast-message {
    font-size: var(--font-size-sm);
    opacity: 0.9;
}

.toast-close {
    background: none;
    border: none;
    color: inherit;
    font-size: 1.25rem;
    cursor: pointer;
    opacity: 0.7;
    transition: var(--transition);
    padding: 0;
    line-height: 1;
}

.toast-close:hover {
    opacity: 1;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Action Buttons in Detail Panel */
.detail-actions {
    display: flex;
    gap: 0.5rem;
    margin-top: 1rem;
}

.detail-actions .btn {
    flex: 1;
    margin-bottom: 0;
}

/* Connection Indicators */
.connection-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 0.5rem;
}

.connection-indicator.connected {
    background-color: var(--success);
}

.connection-indicator.disconnected {
    background-color: var(--danger);
}

.connection-indicator.unknown {
    background-color: var(--warning);
}

/* Terminal Container */
.terminal-container {
    flex-shrink: 0;
    border-top: 1px solid var(--border-color);
    background-color: var(--bg-secondary);
}

.terminal-container.hidden {
    display: none;
}

/* Layout adjustments for terminal */
.content-area.terminal-open .graph-container {
    flex: 1;
    min-height: 200px;
}

.content-area.terminal-open .terminal-container {
    height: 300px;
    min-height: 200px;
    max-height: 50vh;
}
