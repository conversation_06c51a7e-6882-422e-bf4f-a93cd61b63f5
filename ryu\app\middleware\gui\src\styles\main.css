/* CSS Custom Properties (Variables) */
:root {
    /* Light theme colors */
    --bg-primary: #ffffff;
    --bg-secondary: #f8f9fa;
    --bg-tertiary: #e9ecef;
    --text-primary: #212529;
    --text-secondary: #6c757d;
    --text-muted: #adb5bd;
    --border-color: #dee2e6;
    --shadow: rgba(0, 0, 0, 0.1);
    
    /* Accent colors */
    --primary: #007bff;
    --primary-hover: #0056b3;
    --secondary: #6c757d;
    --success: #28a745;
    --warning: #ffc107;
    --danger: #dc3545;
    --info: #17a2b8;
    
    /* Graph colors */
    --switch-openflow: #007bff;
    --switch-p4runtime: #28a745;
    --host-color: #ffc107;
    --link-color: #6c757d;
    --link-active: #007bff;
    
    /* Layout */
    --header-height: 60px;
    --sidebar-width: 320px;
    --border-radius: 8px;
    --transition: all 0.3s ease;
    
    /* Typography */
    --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-bold: 600;
}

/* Dark theme */
[data-theme="dark"] {
    --bg-primary: #1a1a1a;
    --bg-secondary: #2d2d2d;
    --bg-tertiary: #404040;
    --text-primary: #ffffff;
    --text-secondary: #b3b3b3;
    --text-muted: #808080;
    --border-color: #404040;
    --shadow: rgba(0, 0, 0, 0.3);
}

/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    line-height: 1.5;
    color: var(--text-primary);
    background-color: var(--bg-primary);
    overflow: hidden;
}

/* Header */
.header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: var(--header-height);
    background-color: var(--bg-secondary);
    border-bottom: 1px solid var(--border-color);
    z-index: 1000;
    box-shadow: 0 2px 4px var(--shadow);
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    padding: 0 1rem;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.header-title {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
}

.header-icon {
    font-size: 1.5rem;
}

.header-subtitle {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.header-right {
    display: flex;
    align-items: center;
    gap: 1rem;
}

/* Connection Status */
.connection-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.25rem 0.75rem;
    border-radius: var(--border-radius);
    background-color: var(--bg-tertiary);
    font-size: var(--font-size-sm);
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: var(--warning);
    animation: pulse 2s infinite;
}

.status-indicator.connected {
    background-color: var(--success);
    animation: none;
}

.status-indicator.disconnected {
    background-color: var(--danger);
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Theme Toggle */
.theme-toggle {
    background: none;
    border: none;
    font-size: 1.25rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
    color: var(--text-primary);
}

.theme-toggle:hover {
    background-color: var(--bg-tertiary);
}

/* Main Content */
.main-content {
    display: flex;
    margin-top: var(--header-height);
    height: calc(100vh - var(--header-height));
}

/* Sidebar */
.sidebar {
    width: var(--sidebar-width);
    background-color: var(--bg-secondary);
    border-right: 1px solid var(--border-color);
    overflow-y: auto;
    transition: var(--transition);
    z-index: 100;
}

.sidebar.collapsed {
    width: 0;
    overflow: hidden;
}

.sidebar-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
    background-color: var(--bg-tertiary);
}

.sidebar-header h3 {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-medium);
}

.sidebar-toggle {
    background: none;
    border: none;
    font-size: 1.25rem;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
    color: var(--text-primary);
}

.sidebar-toggle:hover {
    background-color: var(--bg-primary);
}

/* Control Sections */
.control-section {
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.control-section:last-child {
    border-bottom: none;
}

.control-section h4 {
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-medium);
    margin-bottom: 0.75rem;
    color: var(--text-primary);
}

/* Search */
.search-container {
    position: relative;
    margin-bottom: 1rem;
}

.search-input {
    width: 100%;
    padding: 0.5rem 2rem 0.5rem 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background-color: var(--bg-primary);
    color: var(--text-primary);
    font-size: var(--font-size-sm);
    transition: var(--transition);
}

.search-input:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.search-clear {
    position: absolute;
    right: 0.5rem;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    font-size: 1.25rem;
    cursor: pointer;
    color: var(--text-muted);
    transition: var(--transition);
}

.search-clear:hover {
    color: var(--text-primary);
}

/* Filter Group */
.filter-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.filter-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    font-size: var(--font-size-sm);
    color: var(--text-primary);
}

.filter-label input[type="checkbox"] {
    width: 16px;
    height: 16px;
    accent-color: var(--primary);
}

.count {
    margin-left: auto;
    padding: 0.125rem 0.375rem;
    background-color: var(--bg-tertiary);
    border-radius: 12px;
    font-size: 0.75rem;
    color: var(--text-secondary);
}

/* Layout Select */
.layout-select {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background-color: var(--bg-primary);
    color: var(--text-primary);
    font-size: var(--font-size-sm);
    margin-bottom: 0.75rem;
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem 1rem;
    border: none;
    border-radius: var(--border-radius);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition);
    width: 100%;
    margin-bottom: 0.5rem;
}

.btn:last-child {
    margin-bottom: 0;
}

.btn-primary {
    background-color: var(--primary);
    color: white;
}

.btn-primary:hover {
    background-color: var(--primary-hover);
}

.btn-secondary {
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.btn-secondary:hover {
    background-color: var(--bg-primary);
}

/* Statistics Grid */
.stats-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 0.75rem;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem;
    background-color: var(--bg-primary);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
}

.stat-label {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.stat-value {
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-medium);
    color: var(--text-primary);
}
