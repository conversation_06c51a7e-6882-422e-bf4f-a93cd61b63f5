/* Terminal Panel Styles */

/* Terminal Panel Container */
.terminal-panel {
    display: flex;
    flex-direction: column;
    background-color: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: 0 4px 8px var(--shadow);
    font-family: 'Courier New', 'Monaco', 'Menlo', monospace;
    font-size: 13px;
    line-height: 1.4;
    height: 400px;
    min-height: 200px;
    max-height: 80vh;
    position: relative;
    overflow: hidden;
}

/* Terminal Header */
.terminal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.5rem 1rem;
    background-color: var(--bg-tertiary);
    border-bottom: 1px solid var(--border-color);
    border-radius: var(--border-radius) var(--border-radius) 0 0;
    flex-shrink: 0;
}

.terminal-title {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: var(--font-weight-medium);
    color: var(--text-primary);
}

.terminal-icon {
    font-size: 1.1rem;
}

.terminal-controls {
    display: flex;
    gap: 0.25rem;
}

.terminal-btn {
    background: none;
    border: none;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    cursor: pointer;
    color: var(--text-secondary);
    transition: var(--transition);
    font-size: 0.9rem;
}

.terminal-btn:hover {
    background-color: var(--bg-primary);
    color: var(--text-primary);
}

.terminal-btn:active {
    background-color: var(--border-color);
}

/* Terminal Filters */
.terminal-filters {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background-color: var(--bg-primary);
    border-bottom: 1px solid var(--border-color);
    flex-wrap: wrap;
    flex-shrink: 0;
}

.filter-group {
    position: relative;
    display: flex;
    align-items: center;
}

.filter-input {
    padding: 0.25rem 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    font-size: 12px;
    width: 120px;
    transition: var(--transition);
}

.filter-input:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.filter-input::placeholder {
    color: var(--text-muted);
}

.filter-select {
    padding: 0.25rem 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    font-size: 12px;
    min-width: 100px;
    cursor: pointer;
}

.filter-clear {
    position: absolute;
    right: 0.25rem;
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    font-size: 14px;
    padding: 0;
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.filter-clear:hover {
    color: var(--text-primary);
}

.filter-clear-all {
    padding: 0.25rem 0.5rem;
    background-color: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    color: var(--text-secondary);
    cursor: pointer;
    font-size: 12px;
    transition: var(--transition);
}

.filter-clear-all:hover {
    background-color: var(--bg-secondary);
    color: var(--text-primary);
}

/* Terminal Content */
.terminal-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.terminal-output {
    flex: 1;
    overflow-y: auto;
    padding: 0.5rem;
    background-color: var(--bg-primary);
    color: var(--text-primary);
}

/* Terminal Welcome */
.terminal-welcome {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    text-align: center;
    color: var(--text-secondary);
}

.terminal-welcome-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.terminal-welcome-text h3 {
    margin: 0 0 0.5rem 0;
    color: var(--text-primary);
    font-weight: var(--font-weight-medium);
}

.terminal-welcome-text p {
    margin: 0 0 1rem 0;
    color: var(--text-secondary);
}

.terminal-status {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.5rem;
    background-color: var(--bg-secondary);
    border-radius: 4px;
    font-size: 12px;
}

/* Terminal Events */
.terminal-events {
    display: flex;
    flex-direction: column;
}

.terminal-event {
    padding: 0.25rem 0.5rem;
    border-left: 3px solid transparent;
    transition: var(--transition);
    cursor: pointer;
}

.terminal-event:hover {
    background-color: var(--bg-secondary);
}

.terminal-event.selected {
    background-color: var(--bg-tertiary);
    border-left-color: var(--primary);
}

/* Event Colors */
.terminal-event.event-success {
    border-left-color: var(--success);
}

.terminal-event.event-warning {
    border-left-color: var(--warning);
}

.terminal-event.event-danger {
    border-left-color: var(--danger);
}

.terminal-event.event-info {
    border-left-color: var(--info);
}

.terminal-event.event-secondary {
    border-left-color: var(--secondary);
}

.terminal-event.event-muted {
    border-left-color: var(--text-muted);
}

/* Event Line */
.event-line {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 12px;
}

.event-timestamp {
    color: var(--text-muted);
    font-weight: var(--font-weight-normal);
    min-width: 80px;
    flex-shrink: 0;
}

.event-icon {
    font-size: 14px;
    width: 16px;
    text-align: center;
    flex-shrink: 0;
}

.event-type {
    color: var(--text-secondary);
    font-weight: var(--font-weight-medium);
    min-width: 100px;
    flex-shrink: 0;
    text-transform: uppercase;
    font-size: 11px;
}

.event-message {
    color: var(--text-primary);
    flex: 1;
    word-break: break-word;
}

/* Terminal Stats */
.terminal-stats {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.5rem 1rem;
    background-color: var(--bg-tertiary);
    border-top: 1px solid var(--border-color);
    font-size: 11px;
    flex-shrink: 0;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.stat-label {
    color: var(--text-secondary);
    font-weight: var(--font-weight-medium);
}

.stat-value {
    color: var(--text-primary);
    font-weight: var(--font-weight-bold);
    font-family: 'Courier New', monospace;
}

/* Terminal Resize Handle */
.terminal-resize-handle {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    cursor: ns-resize;
    background: transparent;
    z-index: 10;
}

.terminal-resize-handle:hover {
    background-color: var(--primary);
    opacity: 0.5;
}

/* Event Detail Popup */
.terminal-event-popup {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    animation: fadeIn 0.2s ease-out;
}

.terminal-event-popup > div {
    background-color: var(--bg-primary);
    border-radius: var(--border-radius);
    box-shadow: 0 8px 16px var(--shadow);
    max-width: 600px;
    max-height: 80vh;
    overflow-y: auto;
    animation: slideIn 0.2s ease-out;
}

.popup-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
    background-color: var(--bg-secondary);
    border-bottom: 1px solid var(--border-color);
    border-radius: var(--border-radius) var(--border-radius) 0 0;
}

.popup-header h3 {
    margin: 0;
    color: var(--text-primary);
    font-size: var(--font-size-lg);
}

.popup-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--text-secondary);
    padding: 0;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: var(--transition);
}

.popup-close:hover {
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
}

.popup-content {
    padding: 1rem;
}

.detail-section {
    margin-bottom: 1rem;
}

.detail-section:last-child {
    margin-bottom: 0;
}

.detail-section strong {
    color: var(--text-primary);
    font-weight: var(--font-weight-medium);
}

.detail-section pre {
    background-color: var(--bg-secondary);
    padding: 0.5rem;
    border-radius: 4px;
    font-size: 11px;
    overflow-x: auto;
    margin: 0.5rem 0 0 0;
    color: var(--text-primary);
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from { 
        opacity: 0;
        transform: translateY(-20px);
    }
    to { 
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .terminal-filters {
        flex-direction: column;
        align-items: stretch;
        gap: 0.25rem;
    }
    
    .filter-group {
        width: 100%;
    }
    
    .filter-input,
    .filter-select {
        width: 100%;
    }
    
    .terminal-stats {
        flex-wrap: wrap;
        gap: 0.5rem;
    }
    
    .event-line {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.25rem;
    }
    
    .event-timestamp,
    .event-type {
        min-width: auto;
    }
    
    .terminal-event-popup > div {
        margin: 1rem;
        max-width: calc(100vw - 2rem);
    }
}

/* Dark theme specific adjustments */
[data-theme="dark"] .terminal-panel {
    background-color: #1a1a1a;
    border-color: #404040;
}

[data-theme="dark"] .terminal-output {
    background-color: #0d1117;
}

[data-theme="dark"] .filter-input,
[data-theme="dark"] .filter-select {
    background-color: #21262d;
    border-color: #30363d;
    color: #f0f6fc;
}

[data-theme="dark"] .terminal-event:hover {
    background-color: #21262d;
}

[data-theme="dark"] .terminal-event.selected {
    background-color: #30363d;
}

/* High contrast mode */
@media (prefers-contrast: high) {
    .terminal-event {
        border-left-width: 4px;
    }
    
    .event-line {
        font-weight: var(--font-weight-medium);
    }
    
    .terminal-btn:hover {
        outline: 2px solid var(--primary);
    }
}
