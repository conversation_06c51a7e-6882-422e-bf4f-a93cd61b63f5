# Copyright (C) 2012 Nippon Telegraph and Telephone Corporation.
# Copyright (C) 2012 <PERSON><PERSON> <yamahata at private email ne jp>
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or
# implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Do NOT edit.
# This is automatically generated by ryu/lib/ovs/vsctl.py
# created based on version 7.14.0


OVSREC_DB_NAME = 'Open_vSwitch'

OVSREC_TABLE_AUTOATTACH = 'AutoAttach'
OVSREC_AUTOATTACH_COL_MAPPINGS = 'mappings'
OVSREC_AUTOATTACH_COL_SYSTEM_DESCRIPTION = 'system_description'
OVSREC_AUTOATTACH_COL_SYSTEM_NAME = 'system_name'

OVSREC_TABLE_BRIDGE = 'Bridge'
OVSREC_BRIDGE_COL_AUTO_ATTACH = 'auto_attach'
OVSREC_BRIDGE_COL_CONTROLLER = 'controller'
OVSREC_BRIDGE_COL_DATAPATH_ID = 'datapath_id'
OVSREC_BRIDGE_COL_DATAPATH_TYPE = 'datapath_type'
OVSREC_BRIDGE_COL_DATAPATH_VERSION = 'datapath_version'
OVSREC_BRIDGE_COL_EXTERNAL_IDS = 'external_ids'
OVSREC_BRIDGE_COL_FAIL_MODE = 'fail_mode'
OVSREC_BRIDGE_COL_FLOOD_VLANS = 'flood_vlans'
OVSREC_BRIDGE_COL_FLOW_TABLES = 'flow_tables'
OVSREC_BRIDGE_COL_IPFIX = 'ipfix'
OVSREC_BRIDGE_COL_MCAST_SNOOPING_ENABLE = 'mcast_snooping_enable'
OVSREC_BRIDGE_COL_MIRRORS = 'mirrors'
OVSREC_BRIDGE_COL_NAME = 'name'
OVSREC_BRIDGE_COL_NETFLOW = 'netflow'
OVSREC_BRIDGE_COL_OTHER_CONFIG = 'other_config'
OVSREC_BRIDGE_COL_PORTS = 'ports'
OVSREC_BRIDGE_COL_PROTOCOLS = 'protocols'
OVSREC_BRIDGE_COL_RSTP_ENABLE = 'rstp_enable'
OVSREC_BRIDGE_COL_RSTP_STATUS = 'rstp_status'
OVSREC_BRIDGE_COL_SFLOW = 'sflow'
OVSREC_BRIDGE_COL_STATUS = 'status'
OVSREC_BRIDGE_COL_STP_ENABLE = 'stp_enable'

OVSREC_TABLE_CONTROLLER = 'Controller'
OVSREC_CONTROLLER_COL_CONNECTION_MODE = 'connection_mode'
OVSREC_CONTROLLER_COL_CONTROLLER_BURST_LIMIT = 'controller_burst_limit'
OVSREC_CONTROLLER_COL_CONTROLLER_RATE_LIMIT = 'controller_rate_limit'
OVSREC_CONTROLLER_COL_ENABLE_ASYNC_MESSAGES = 'enable_async_messages'
OVSREC_CONTROLLER_COL_EXTERNAL_IDS = 'external_ids'
OVSREC_CONTROLLER_COL_INACTIVITY_PROBE = 'inactivity_probe'
OVSREC_CONTROLLER_COL_IS_CONNECTED = 'is_connected'
OVSREC_CONTROLLER_COL_LOCAL_GATEWAY = 'local_gateway'
OVSREC_CONTROLLER_COL_LOCAL_IP = 'local_ip'
OVSREC_CONTROLLER_COL_LOCAL_NETMASK = 'local_netmask'
OVSREC_CONTROLLER_COL_MAX_BACKOFF = 'max_backoff'
OVSREC_CONTROLLER_COL_OTHER_CONFIG = 'other_config'
OVSREC_CONTROLLER_COL_ROLE = 'role'
OVSREC_CONTROLLER_COL_STATUS = 'status'
OVSREC_CONTROLLER_COL_TARGET = 'target'

OVSREC_TABLE_FLOW_SAMPLE_COLLECTOR_SET = 'Flow_Sample_Collector_Set'
OVSREC_FLOW_SAMPLE_COLLECTOR_SET_COL_BRIDGE = 'bridge'
OVSREC_FLOW_SAMPLE_COLLECTOR_SET_COL_EXTERNAL_IDS = 'external_ids'
OVSREC_FLOW_SAMPLE_COLLECTOR_SET_COL_ID = 'id'
OVSREC_FLOW_SAMPLE_COLLECTOR_SET_COL_IPFIX = 'ipfix'

OVSREC_TABLE_FLOW_TABLE = 'Flow_Table'
OVSREC_FLOW_TABLE_COL_EXTERNAL_IDS = 'external_ids'
OVSREC_FLOW_TABLE_COL_FLOW_LIMIT = 'flow_limit'
OVSREC_FLOW_TABLE_COL_GROUPS = 'groups'
OVSREC_FLOW_TABLE_COL_NAME = 'name'
OVSREC_FLOW_TABLE_COL_OVERFLOW_POLICY = 'overflow_policy'
OVSREC_FLOW_TABLE_COL_PREFIXES = 'prefixes'

OVSREC_TABLE_IPFIX = 'IPFIX'
OVSREC_IPFIX_COL_CACHE_ACTIVE_TIMEOUT = 'cache_active_timeout'
OVSREC_IPFIX_COL_CACHE_MAX_FLOWS = 'cache_max_flows'
OVSREC_IPFIX_COL_EXTERNAL_IDS = 'external_ids'
OVSREC_IPFIX_COL_OBS_DOMAIN_ID = 'obs_domain_id'
OVSREC_IPFIX_COL_OBS_POINT_ID = 'obs_point_id'
OVSREC_IPFIX_COL_OTHER_CONFIG = 'other_config'
OVSREC_IPFIX_COL_SAMPLING = 'sampling'
OVSREC_IPFIX_COL_TARGETS = 'targets'

OVSREC_TABLE_INTERFACE = 'Interface'
OVSREC_INTERFACE_COL_ADMIN_STATE = 'admin_state'
OVSREC_INTERFACE_COL_BFD = 'bfd'
OVSREC_INTERFACE_COL_BFD_STATUS = 'bfd_status'
OVSREC_INTERFACE_COL_CFM_FAULT = 'cfm_fault'
OVSREC_INTERFACE_COL_CFM_FAULT_STATUS = 'cfm_fault_status'
OVSREC_INTERFACE_COL_CFM_FLAP_COUNT = 'cfm_flap_count'
OVSREC_INTERFACE_COL_CFM_HEALTH = 'cfm_health'
OVSREC_INTERFACE_COL_CFM_MPID = 'cfm_mpid'
OVSREC_INTERFACE_COL_CFM_REMOTE_MPIDS = 'cfm_remote_mpids'
OVSREC_INTERFACE_COL_CFM_REMOTE_OPSTATE = 'cfm_remote_opstate'
OVSREC_INTERFACE_COL_DUPLEX = 'duplex'
OVSREC_INTERFACE_COL_ERROR = 'error'
OVSREC_INTERFACE_COL_EXTERNAL_IDS = 'external_ids'
OVSREC_INTERFACE_COL_IFINDEX = 'ifindex'
OVSREC_INTERFACE_COL_INGRESS_POLICING_BURST = 'ingress_policing_burst'
OVSREC_INTERFACE_COL_INGRESS_POLICING_RATE = 'ingress_policing_rate'
OVSREC_INTERFACE_COL_LACP_CURRENT = 'lacp_current'
OVSREC_INTERFACE_COL_LINK_RESETS = 'link_resets'
OVSREC_INTERFACE_COL_LINK_SPEED = 'link_speed'
OVSREC_INTERFACE_COL_LINK_STATE = 'link_state'
OVSREC_INTERFACE_COL_LLDP = 'lldp'
OVSREC_INTERFACE_COL_MAC = 'mac'
OVSREC_INTERFACE_COL_MAC_IN_USE = 'mac_in_use'
OVSREC_INTERFACE_COL_MTU = 'mtu'
OVSREC_INTERFACE_COL_MTU_REQUEST = 'mtu_request'
OVSREC_INTERFACE_COL_NAME = 'name'
OVSREC_INTERFACE_COL_OFPORT = 'ofport'
OVSREC_INTERFACE_COL_OFPORT_REQUEST = 'ofport_request'
OVSREC_INTERFACE_COL_OPTIONS = 'options'
OVSREC_INTERFACE_COL_OTHER_CONFIG = 'other_config'
OVSREC_INTERFACE_COL_STATISTICS = 'statistics'
OVSREC_INTERFACE_COL_STATUS = 'status'
OVSREC_INTERFACE_COL_TYPE = 'type'

OVSREC_TABLE_MANAGER = 'Manager'
OVSREC_MANAGER_COL_CONNECTION_MODE = 'connection_mode'
OVSREC_MANAGER_COL_EXTERNAL_IDS = 'external_ids'
OVSREC_MANAGER_COL_INACTIVITY_PROBE = 'inactivity_probe'
OVSREC_MANAGER_COL_IS_CONNECTED = 'is_connected'
OVSREC_MANAGER_COL_MAX_BACKOFF = 'max_backoff'
OVSREC_MANAGER_COL_OTHER_CONFIG = 'other_config'
OVSREC_MANAGER_COL_STATUS = 'status'
OVSREC_MANAGER_COL_TARGET = 'target'

OVSREC_TABLE_MIRROR = 'Mirror'
OVSREC_MIRROR_COL_EXTERNAL_IDS = 'external_ids'
OVSREC_MIRROR_COL_NAME = 'name'
OVSREC_MIRROR_COL_OUTPUT_PORT = 'output_port'
OVSREC_MIRROR_COL_OUTPUT_VLAN = 'output_vlan'
OVSREC_MIRROR_COL_SELECT_ALL = 'select_all'
OVSREC_MIRROR_COL_SELECT_DST_PORT = 'select_dst_port'
OVSREC_MIRROR_COL_SELECT_SRC_PORT = 'select_src_port'
OVSREC_MIRROR_COL_SELECT_VLAN = 'select_vlan'
OVSREC_MIRROR_COL_SNAPLEN = 'snaplen'
OVSREC_MIRROR_COL_STATISTICS = 'statistics'

OVSREC_TABLE_NETFLOW = 'NetFlow'
OVSREC_NETFLOW_COL_ACTIVE_TIMEOUT = 'active_timeout'
OVSREC_NETFLOW_COL_ADD_ID_TO_INTERFACE = 'add_id_to_interface'
OVSREC_NETFLOW_COL_ENGINE_ID = 'engine_id'
OVSREC_NETFLOW_COL_ENGINE_TYPE = 'engine_type'
OVSREC_NETFLOW_COL_EXTERNAL_IDS = 'external_ids'
OVSREC_NETFLOW_COL_TARGETS = 'targets'

OVSREC_TABLE_OPEN_VSWITCH = 'Open_vSwitch'
OVSREC_OPEN_VSWITCH_COL_BRIDGES = 'bridges'
OVSREC_OPEN_VSWITCH_COL_CUR_CFG = 'cur_cfg'
OVSREC_OPEN_VSWITCH_COL_DATAPATH_TYPES = 'datapath_types'
OVSREC_OPEN_VSWITCH_COL_DB_VERSION = 'db_version'
OVSREC_OPEN_VSWITCH_COL_EXTERNAL_IDS = 'external_ids'
OVSREC_OPEN_VSWITCH_COL_IFACE_TYPES = 'iface_types'
OVSREC_OPEN_VSWITCH_COL_MANAGER_OPTIONS = 'manager_options'
OVSREC_OPEN_VSWITCH_COL_NEXT_CFG = 'next_cfg'
OVSREC_OPEN_VSWITCH_COL_OTHER_CONFIG = 'other_config'
OVSREC_OPEN_VSWITCH_COL_OVS_VERSION = 'ovs_version'
OVSREC_OPEN_VSWITCH_COL_SSL = 'ssl'
OVSREC_OPEN_VSWITCH_COL_STATISTICS = 'statistics'
OVSREC_OPEN_VSWITCH_COL_SYSTEM_TYPE = 'system_type'
OVSREC_OPEN_VSWITCH_COL_SYSTEM_VERSION = 'system_version'

OVSREC_TABLE_PORT = 'Port'
OVSREC_PORT_COL_BOND_ACTIVE_SLAVE = 'bond_active_slave'
OVSREC_PORT_COL_BOND_DOWNDELAY = 'bond_downdelay'
OVSREC_PORT_COL_BOND_FAKE_IFACE = 'bond_fake_iface'
OVSREC_PORT_COL_BOND_MODE = 'bond_mode'
OVSREC_PORT_COL_BOND_UPDELAY = 'bond_updelay'
OVSREC_PORT_COL_EXTERNAL_IDS = 'external_ids'
OVSREC_PORT_COL_FAKE_BRIDGE = 'fake_bridge'
OVSREC_PORT_COL_INTERFACES = 'interfaces'
OVSREC_PORT_COL_LACP = 'lacp'
OVSREC_PORT_COL_MAC = 'mac'
OVSREC_PORT_COL_NAME = 'name'
OVSREC_PORT_COL_OTHER_CONFIG = 'other_config'
OVSREC_PORT_COL_QOS = 'qos'
OVSREC_PORT_COL_RSTP_STATISTICS = 'rstp_statistics'
OVSREC_PORT_COL_RSTP_STATUS = 'rstp_status'
OVSREC_PORT_COL_STATISTICS = 'statistics'
OVSREC_PORT_COL_STATUS = 'status'
OVSREC_PORT_COL_TAG = 'tag'
OVSREC_PORT_COL_TRUNKS = 'trunks'
OVSREC_PORT_COL_VLAN_MODE = 'vlan_mode'

OVSREC_TABLE_QOS = 'QoS'
OVSREC_QOS_COL_EXTERNAL_IDS = 'external_ids'
OVSREC_QOS_COL_OTHER_CONFIG = 'other_config'
OVSREC_QOS_COL_QUEUES = 'queues'
OVSREC_QOS_COL_TYPE = 'type'

OVSREC_TABLE_QUEUE = 'Queue'
OVSREC_QUEUE_COL_DSCP = 'dscp'
OVSREC_QUEUE_COL_EXTERNAL_IDS = 'external_ids'
OVSREC_QUEUE_COL_OTHER_CONFIG = 'other_config'

OVSREC_TABLE_SSL = 'SSL'
OVSREC_SSL_COL_BOOTSTRAP_CA_CERT = 'bootstrap_ca_cert'
OVSREC_SSL_COL_CA_CERT = 'ca_cert'
OVSREC_SSL_COL_CERTIFICATE = 'certificate'
OVSREC_SSL_COL_EXTERNAL_IDS = 'external_ids'
OVSREC_SSL_COL_PRIVATE_KEY = 'private_key'

OVSREC_TABLE_SFLOW = 'sFlow'
OVSREC_SFLOW_COL_AGENT = 'agent'
OVSREC_SFLOW_COL_EXTERNAL_IDS = 'external_ids'
OVSREC_SFLOW_COL_HEADER = 'header'
OVSREC_SFLOW_COL_POLLING = 'polling'
OVSREC_SFLOW_COL_SAMPLING = 'sampling'
OVSREC_SFLOW_COL_TARGETS = 'targets'
