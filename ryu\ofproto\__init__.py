# Copyright (C) 2013,2014 Nippon Telegraph and Telephone Corporation.
# Copyright (C) 2013 <PERSON><PERSON> <yamahata at private email ne jp>
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or
# implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import glob
import inspect
import os.path

from ryu.ofproto import ofproto_protocol


def get_ofp_modules():
    """get modules pair for the constants and parser of OF-wire of
    a given OF version.
    """
    return ofproto_protocol._versions


def get_ofp_module(ofp_version):
    """get modules pair for the constants and parser of OF-wire of
    a given OF version.
    """
    return get_ofp_modules()[ofp_version]
