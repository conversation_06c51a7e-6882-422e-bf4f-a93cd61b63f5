# Copyright (C) 2017 Nippon Telegraph and Telephone Corporation.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or
# implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""
Events generated by Zebra Server service.
"""

from ryu.controller.event import EventBase


class EventZServerBase(EventBase):
    """
    The base class for the event generated by ZServer.
    """


class EventZClientConnected(EventZServerBase):
    """
    The event class for notifying the connection from Zebra client.
    """

    def __init__(self, zclient):
        super(EventZClientConnected, self).__init__()
        self.zclient = zclient


class EventZClientDisconnected(EventZServerBase):
    """
    The event class for notifying the disconnection to Zebra client.
    """

    def __init__(self, zclient):
        super(EventZClientDisconnected, self).__init__()
        self.zclient = zclient
