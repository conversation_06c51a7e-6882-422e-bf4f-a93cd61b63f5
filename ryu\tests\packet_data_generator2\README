This directory contains a small C program to generate
OpenFlow packet binary files [1].  They are used by some of
unit tests [2].  As we have the generated files in the repository,
you don't need to build this program unless you want to (re-)generate
the files for some reasons, typically adding new test cases.

[1] ryu/tests/packet_data/of*/libofproto-*.packet
[2] ryu.tests.unit.ofproto.test_parser

The program requires libofproto library provided by Open vSwitch.
It's a little tricky to build because Open vSwitch distribution
normally does not install the corresponding C header files.  You need
both of the source distribution of Open vSwitch and matching installed
libraries, and override OVS_SRC and SRC make variables to point those
directries respectively.
