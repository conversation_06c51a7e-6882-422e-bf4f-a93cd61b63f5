%% Copyright (C) 2013 Nippon Telegraph and Telephone Corporation.
%% Copyright (C) 2013 YAMAMOT<PERSON> <yamamoto at valinux co jp>
%%
%% Licensed under the Apache License, Version 2.0 (the "License");
%% you may not use this file except in compliance with the License.
%% You may obtain a copy of the License at
%%
%%    http://www.apache.org/licenses/LICENSE-2.0
%%
%% Unless required by applicable law or agreed to in writing, software
%% distributed under the License is distributed on an "AS IS" BASIS,
%% WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or
%% implied.
%% See the License for the specific language governing permissions and
%% limitations under the License.

{deps,
 [{of_protocol, ".*",
%  {git, "https://github.com/FlowForwarding/of_protocol.git",
%   "5d6a938bcac91d03e4542845bc014a271bab363a"}},
% use local branch for merge of upstream v5 branch with pull 66 and 71
%   https://github.com/FlowForwarding/of_protocol/tree/v5
%   https://github.com/FlowForwarding/of_protocol/pull/66
%   https://github.com/FlowForwarding/of_protocol/pull/71
  {git, "https://github.com/horms/of_protocol.git",
   "6764e8f0ed5c4dee913310bf2bd908ab1768603e"}},
  {flower, ".*",
   {git, "http://github.com/travelping/flower.git",
    "d783d8f722cb1eb2fa598d4521b309cfcc703fdb"}}]}.
