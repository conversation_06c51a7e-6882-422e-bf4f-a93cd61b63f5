[tox]
envlist = py38,py39,py310,py311,py312,pycodestyle,autopep8,mypy

[gh-actions]
python =
  3.8: py38
  3.9: py39, pycodestyle, autopep8, mypy
  3.10: py310
  3.11: py311
  3.12: py312

[testenv]
deps =
  -r{toxinidir}/tools/pip-requires
  -r{toxinidir}/tools/optional-requires
  -r{toxinidir}/tools/test-requires
usedevelop = True
passenv = PYTEST_*
# Note: To check whether tools/pip-requires satisfies the requirements
# for running Ryu, the following runs ryu-manager before installing
# the additional requirements.
commands =
  pytest {posargs}

[testenv:scenario]
commands =
  python ryu/tests/integrated/run_test.py

[testenv:py39]
commands =
  {[testenv]commands}
  {[testenv:scenario]commands}

[testenv:pycodestyle]
deps =
  -U
  --no-cache-dir
  pycodestyle
commands =
  pycodestyle

[testenv:autopep8]
# If some errors displayed with this test, please reformat codes with the
# following command first.
# $ autopep8 --recursive --in-place ryu/
whitelist_externals=bash
deps =
  -U
  --no-cache-dir
  autopep8
commands =
  bash -c 'test -z "$(autopep8 --recursive --diff ryu/)"'

[testenv:mypy]
deps =
  -r{toxinidir}/tools/pip-requires
  mypy>=1.0.0
  types-setuptools
commands =
  mypy ryu --ignore-missing-imports

[pycodestyle]
exclude = pbr-*,.venv,.tox,.git,doc,dist,tools,vcsversion.py,.pyc,ryu/contrib
# W503: line break before binary operator
# W504: line break after binary operator
# E116: unexpected indentation (comment)
# E402: module level import not at top of file
# E501: line too long (>79 characters)
# E722: do not use bare except, specify exception instead
# E731: do not assign a lambda expression, use a def
# E741: do not use variables named 'l', 'O', or 'I'
ignore = W503,W504,E116,E402,E501,E722,E731,E741

[pep8]
exclude = pbr-*,.venv,.tox,.git,doc,dist,tools,vcsversion.py,.pyc,ryu/contrib
ignore = W503,E116,E402,E501,E722,E731,E741
